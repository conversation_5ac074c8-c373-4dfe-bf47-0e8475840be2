$(document).ready(function() {
  
    const urlParams = new URLSearchParams(window.location.search);
    const errorCode = urlParams.get('error');
    if (errorCode) {
        //alertMensajes(getErrorMessage(errorCode));
        window.history.replaceState({}, document.title, window.location.pathname);
    }

    var mensaje = getCookieValue('mensaje');
    if (mensaje !== "" && mensaje !== null) {
      if (mensaje !=="default"){
        alertMensajes(mensaje);
      }
    }
    
    sessionStorage.clear();
    limpiarCampos();

    // Interceptar el formulario de login
    $('form[action="/login"]').on('submit', function(e) {
        e.preventDefault();

        const usuario = $('[name="USUARIO"]').val();
        const claveAcceso = $('[name="CLAVE_ACCESO"]').val();

        // Validar campos
        if (!usuario || !claveAcceso) {
            alertMensajes('Por favor complete todos los campos');
            return;
        }

        // Mostrar spinner
        showSpinner('Verificando credenciales...');

        const clientKey = 'temp_client_key_2024';
        const encryptedPassword = CryptoJS.AES.encrypt(claveAcceso, clientKey).toString();


        const tempLoginData = {
            usuario: usuario,
            claveEncriptada: encryptedPassword,
            timestamp: Date.now(),
            exp: Math.floor(Date.now() / 1000) + (5 * 60) // 5 minutos
        };

        const tempJWT = btoa(JSON.stringify(tempLoginData));
        document.cookie = `tempLoginJWT=${tempJWT}; path=/; max-age=300; SameSite=Lax`;

        // Actualizar mensaje del spinner
        setTimeout(() => {
            updateSpinnerMessage('Procesando autenticación...');
        }, 1000);

        $.ajax({
            url: '/login',
            method: 'POST',
            data: {
                action: 'authenticate'
            },
            success: function(response) {
                // Actualizar mensaje antes de procesar respuesta
                updateSpinnerMessage('Acceso concedido, redirigiendo...');

                // Limpiar cookie temporal
                document.cookie = 'tempLoginJWT=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT';

                // Pequeño delay para mostrar el mensaje de éxito
                setTimeout(() => {
                    // Verificar si hay mensaje de alerta
                    if (response.showAlert && response.alertMessage) {
                        hideSpinner();
                        Swal.fire({
                            text: response.alertMessage,
                            confirmButtonText: 'OK'
                        }).then(() => {
                            window.location.href = '/home';
                        });
                    } else {
                        // Mantener spinner hasta la redirección
                        window.location.href = '/home';
                    }
                }, 800);
            },
            error: function(xhr) {
                // Ocultar spinner en caso de error
                hideSpinner();

                // Limpiar cookie temporal
                document.cookie = 'tempLoginJWT=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT';

                if (xhr.responseJSON && xhr.responseJSON.redirect) {
                    window.location.href = xhr.responseJSON.redirect;
                } else {
                    window.location.href = '/login?error=1';
                }
            }
        });
    });

});

function limpiarCampos(){
  $("[name='USUARIO']").val("");
  $("[name='CLAVE_ACCESO']").val("");

}

function alertMensajes(mensaje){
    Swal.fire(mensaje);
}

function getErrorMessage(errorCode) {
    const errorMessages = {
        '1': 'Credenciales incorrectas',
        '2': 'Error del servidor',
        '3': 'Sesión expirada o datos inválidos',
        '4': 'Error en los datos de autenticación',
        '5': 'Tiempo de sesión agotado',
        '6': 'Error generando token de sesión'
    };
    return errorMessages[errorCode] || 'Error desconocido';
}

function getCookieValue(cookieName) {
  var name = cookieName + "=";
  var decodedCookie = decodeURIComponent(document.cookie);
  var cookieArray = decodedCookie.split(';');   
  for (var i = 0; i < cookieArray.length; i++) {
    var cookie = cookieArray[i];
    while (cookie.charAt(0) === ' ') {
      cookie = cookie.substring(1);
    }
    if (cookie.indexOf(name) === 0) {
      var cookieValue = cookie.substring(name.length, cookie.length);
      try {
        return JSON.parse(cookieValue);
      }catch (error) {
          return cookieValue;
        }
    }
  }
  return null;
}

function redireccionRecuperar(){
  eliminarTodasLasCookies();
  window.location.href = "/recovery";
  
}

function eliminarTodasLasCookies() {
  var cookies = document.cookie.split(";");

  for (var i = 0; i < cookies.length; i++) {
      var cookie = cookies[i];
      var eqPos = cookie.indexOf("=");
      var nombre = eqPos > -1 ? cookie.substring(0, eqPos) : cookie;
      document.cookie = nombre + "=;expires=Thu, 01 Jan 1970 00:00:00 GMT";
  }
}